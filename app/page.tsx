import {Badge} from '@/components/ui/badge';
import {But<PERSON>} from '@/components/ui/button';
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from '@/components/ui/card';
import {ArrowRight, BarChart3, CheckCircle, Shield, Target, TrendingUp, Users, Zap} from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

export default function HomePage() {
  return (
    <div className="min-h-screen" data-has-sidebar="false">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-primary/5 via-background to-accent/5 py-20 lg:py-32">
        <div className="container mx-auto px-4 text-center">
          <div className="flex justify-center mb-8">
            <Image src="/software_maturity_models_logo.webp" alt="Software Maturity Models" width={120} height={120} className="drop-shadow-sm" />
          </div>
          <Badge variant="secondary" className="mb-6 text-sm font-medium">
            See your maturity. Level up your delivery.
          </Badge>
          <h1 className="text-4xl lg:text-6xl font-bold text-balance mb-6 text-foreground">Software Maturity Models</h1>
          <p className="text-xl lg:text-2xl text-muted-foreground text-balance max-w-3xl mx-auto mb-8">
            Clear, comparable models for engineering, product, and delivery.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg" className="text-lg px-8">
              <Link href="/t/architecture">
                Explore Themes
                {' '}
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button variant="outline" size="lg" asChild className="text-lg px-8 bg-transparent">
              <Link href="/t/architecture/architecture-foundations">View Sample Model</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-20 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4 text-balance">How It Works</h2>
            <p className="text-xl text-muted-foreground text-balance max-w-2xl mx-auto">
              Three simple steps to assess and improve your team's maturity
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6">
                <Target className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-xl font-semibold mb-3">1. Pick a Theme</h3>
              <p className="text-muted-foreground">
                Choose from Architecture, Agile Delivery, Code Quality, or DevOps themes
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-accent/10 rounded-full flex items-center justify-center mx-auto mb-6">
                <BarChart3 className="h-8 w-8 text-accent" />
              </div>
              <h3 className="text-xl font-semibold mb-3">2. Assess Your Level</h3>
              <p className="text-muted-foreground">
                Evaluate your current practices against standardized maturity levels
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6">
                <TrendingUp className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-xl font-semibold mb-3">3. Plan Next Steps</h3>
              <p className="text-muted-foreground">Get clear guidance on how to advance to the next maturity level</p>
            </div>
          </div>
        </div>
      </section>

      {/* Who It's For */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4 text-balance">Who It's For</h2>
            <p className="text-xl text-muted-foreground text-balance max-w-2xl mx-auto">
              Designed for teams at every level of the organization
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            <Card className="text-center p-6">
              <CardHeader>
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Users className="h-6 w-6 text-primary" />
                </div>
                <CardTitle>Executives</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-base">
                  Align strategy and funding with maturity. Make informed decisions about
                  technology investments.
                </CardDescription>
              </CardContent>
            </Card>
            <Card className="text-center p-6">
              <CardHeader>
                <div className="w-12 h-12 bg-accent/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Shield className="h-6 w-6 text-accent" />
                </div>
                <CardTitle>Engineering Leaders</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-base">
                  Benchmark practices and plan improvements. Build roadmaps based on proven
                  frameworks.
                </CardDescription>
              </CardContent>
            </Card>
            <Card className="text-center p-6">
              <CardHeader>
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Zap className="h-6 w-6 text-primary" />
                </div>
                <CardTitle>ICs & Product Managers</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-base">
                  Make the case with clear, evidence-based levels. Communicate needs
                  effectively to leadership.
                </CardDescription>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Featured Models */}
      <section className="py-20 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4 text-balance">Featured Models</h2>
            <p className="text-xl text-muted-foreground text-balance max-w-2xl mx-auto">
              Start with these popular maturity assessments
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            <Card className="group hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="text-xl">Architecture Foundations</CardTitle>
                <CardDescription>
                  Core architecture practices including principles, technical
                  debt, and CI/CD pipelines.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button
                  asChild
                  variant="outline"
                  className="w-full group-hover:bg-primary group-hover:text-primary-foreground transition-colors bg-transparent"
                >
                  <Link href="/t/architecture/architecture-foundations">
                    Assess Architecture
                    {' '}
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </CardContent>
            </Card>
            <Card className="group hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="text-xl">Agile Delivery</CardTitle>
                <CardDescription>
                  Team practices, sprint management, and continuous improvement methodologies.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button
                  asChild
                  variant="outline"
                  className="w-full group-hover:bg-primary group-hover:text-primary-foreground transition-colors bg-transparent"
                >
                  <Link href="/t/agile">
                    Assess Delivery
                    {' '}
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </CardContent>
            </Card>
            <Card className="group hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="text-xl">Code Quality & Testing</CardTitle>
                <CardDescription>
                  Testing strategies, code review processes, and quality assurance practices.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button
                  asChild
                  variant="outline"
                  className="w-full group-hover:bg-primary group-hover:text-primary-foreground transition-colors bg-transparent"
                >
                  <Link href="/t/quality">
                    Assess Quality
                    {' '}
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Why Maturity Matters */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4 text-balance">Why Maturity Matters</h2>
            <p className="text-xl text-muted-foreground text-balance max-w-2xl mx-auto">
              Higher maturity leads to measurable business outcomes
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
            <div className="text-center">
              <div className="text-3xl font-bold text-primary mb-2">3x</div>
              <div className="text-sm font-medium text-muted-foreground uppercase tracking-wide mb-2">
                Predictability
              </div>
              <p className="text-sm text-muted-foreground">More reliable delivery timelines</p>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-accent mb-2">50%</div>
              <div className="text-sm font-medium text-muted-foreground uppercase tracking-wide mb-2">Quality</div>
              <p className="text-sm text-muted-foreground">Fewer production incidents</p>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary mb-2">2x</div>
              <div className="text-sm font-medium text-muted-foreground uppercase tracking-wide mb-2">Velocity</div>
              <p className="text-sm text-muted-foreground">Faster feature delivery</p>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-accent mb-2">75%</div>
              <div className="text-sm font-medium text-muted-foreground uppercase tracking-wide mb-2">Risk</div>
              <p className="text-sm text-muted-foreground">Reduction in security issues</p>
            </div>
          </div>
        </div>
      </section>

      {/* Methodology */}
      <section className="py-20 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl lg:text-4xl font-bold mb-6 text-balance">Our Methodology</h2>
            <p className="text-xl text-muted-foreground mb-8 text-balance">
              Built on industry standards with transparent definitions and continuous improvement
            </p>
            <div className="grid md:grid-cols-3 gap-6 mb-8">
              <div className="flex items-center gap-3">
                <CheckCircle className="h-5 w-5 text-primary flex-shrink-0" />
                <span className="text-sm">Transparent definitions</span>
              </div>
              <div className="flex items-center gap-3">
                <CheckCircle className="h-5 w-5 text-primary flex-shrink-0" />
                <span className="text-sm">Versioned updates</span>
              </div>
              <div className="flex items-center gap-3">
                <CheckCircle className="h-5 w-5 text-primary flex-shrink-0" />
                <span className="text-sm">Cited references</span>
              </div>
            </div>
            <Button asChild variant="outline" size="lg">
              <Link href="/methodology">Learn About Our Approach</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Social Proof */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4 text-balance">Inspired By Industry Leaders</h2>
            <p className="text-xl text-muted-foreground text-balance max-w-2xl mx-auto">
              Built on proven frameworks from recognized organizations
            </p>
          </div>
          <div className="flex flex-wrap justify-center items-center gap-8 max-w-4xl mx-auto text-muted-foreground">
            <div className="text-center">
              <div className="font-semibold text-foreground">DORA</div>
              <div className="text-sm">DevOps Research</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-foreground">OWASP SAMM</div>
              <div className="text-sm">Security Assurance</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-foreground">CMMI</div>
              <div className="text-sm">Process Improvement</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-foreground">Team Topologies</div>
              <div className="text-sm">Organizational Design</div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-primary to-accent text-primary-foreground">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl lg:text-4xl font-bold mb-4 text-balance">Ready to Level Up?</h2>
          <p className="text-xl mb-8 text-balance max-w-2xl mx-auto opacity-90">
            Start assessing your team's maturity today and get clear guidance on your next steps.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg" variant="secondary" className="text-lg px-8">
              <Link href="/t/architecture/architecture-foundations">
                Start Assessment
                {' '}
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button
              asChild
              size="lg"
              variant="outline"
              className="text-lg px-8 border-primary-foreground/20 text-primary-foreground hover:bg-primary-foreground hover:text-primary bg-transparent"
            >
              <Link href="/methodology">Learn More</Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
