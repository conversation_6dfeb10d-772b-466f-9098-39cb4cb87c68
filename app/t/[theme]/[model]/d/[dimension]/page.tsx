'use client'; ;
import {use} from 'react';

import {getModel, getSiteConfig, getThemeIndex} from '@/src/lib/content';
import {MarkdownRenderer} from '@/src/components/MarkdownRenderer';
import {Breadcrumbs} from '@/src/components/Breadcrumbs';
import {Button} from '@/components/ui/button';
import {Card, CardContent} from '@/components/ui/card';
import {Badge} from '@/components/ui/badge';
import {ArrowLeft, Copy, ExternalLink} from 'lucide-react';
import Link from 'next/link';
import {notFound} from 'next/navigation';

type DimensionPageProps = {
  params: Promise<{theme: string; model: string; dimension: string}>;
};

export default function DimensionPage(props: DimensionPageProps) {
  const params = use(props.params);
  try {
    const site = getSiteConfig();
    const theme = getThemeIndex(params.theme);
    const model = getModel(params.theme, params.model);
    const levels = model.levels?.length ? model.levels : site.defaultLevels;

    const dimension = model.dimensions.find((d) => d.id === params.dimension);
    if (!dimension) {
      notFound();
    }

    const copyLink = () => {
      if (typeof window !== 'undefined') {
        navigator.clipboard.writeText(window.location.href);
      }
    };

    return (
      <div className="space-y-8">
        <Breadcrumbs
          items={[
            {label: 'Home', href: '/'},
            {label: theme.title, href: `/t/${params.theme}`},
            {label: model.title, href: `/t/${params.theme}/${params.model}`},
            {label: dimension.label},
          ]}
        />

        {/* Header */}
        <div className="space-y-6">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <Button variant="outline" size="sm" asChild>
              <Link href={`/t/${params.theme}/${params.model}`}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to
                {' '}
                {model.title}
              </Link>
            </Button>

            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={copyLink}>
                <Copy className="h-4 w-4 mr-2" />
                Copy Link
              </Button>
              <Button variant="outline" size="sm" asChild>
                <Link href={`/t/${params.theme}/${params.model}`}>
                  <ExternalLink className="h-4 w-4 mr-2" />
                  View Full Model
                </Link>
              </Button>
            </div>
          </div>

          <div className="text-center space-y-4">
            <h1 className="text-4xl font-bold text-balance">{dimension.label}</h1>
            <div className="flex justify-center gap-2">
              <Badge variant="secondary">{theme.title}</Badge>
              <Badge variant="outline">{model.title}</Badge>
            </div>
          </div>
        </div>

        {/* Level progression */}
        <section>
          <h2 className="text-2xl font-semibold mb-6 text-balance">Maturity Progression</h2>
          <div className="grid gap-4">
            {levels.map((level) => (
              <Card
                key={level.id}
                className="relative overflow-hidden"
                style={{borderLeftColor: level.color, borderLeftWidth: '4px'}}
              >
                <CardContent className="p-6">
                  <div className="flex items-start gap-4">
                    <div className="flex-shrink-0">
                      <div
                        className="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-semibold"
                        style={{backgroundColor: level.color}}
                      >
                        {level.id}
                      </div>
                    </div>
                    <div className="flex-1 space-y-2">
                      <h3 className="text-lg font-semibold text-balance">{level.label}</h3>
                      <div className="prose prose-neutral dark:prose-invert max-w-none">
                        <p className="text-pretty leading-relaxed text-muted-foreground">
                          {dimension.cells[level.id] || 'No description available for this level.'}
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* Assessment guidance */}
        {dimension.assessmentMd && (
          <section>
            <h2 className="text-2xl font-semibold mb-4 text-balance">How to Assess</h2>
            <Card>
              <CardContent className="p-6">
                <MarkdownRenderer content={dimension.assessmentMd} />
              </CardContent>
            </Card>
          </section>
        )}

        {/* Next steps */}
        {dimension.nextStepsMd && (
          <section>
            <h2 className="text-2xl font-semibold mb-4 text-balance">Improvement Guidance</h2>
            <Card>
              <CardContent className="p-6">
                <MarkdownRenderer content={dimension.nextStepsMd} />
              </CardContent>
            </Card>
          </section>
        )}

        {/* Related dimensions */}
        <section>
          <h2 className="text-2xl font-semibold mb-4 text-balance">Other Dimensions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {model.dimensions
              .filter((d) => d.id !== dimension.id)
              .map((relatedDimension) => (
                <Card key={relatedDimension.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <h3 className="font-medium text-balance mb-2">{relatedDimension.label}</h3>
                    <Button variant="outline" size="sm" asChild className="w-full bg-transparent">
                      <Link href={`/t/${params.theme}/${params.model}/d/${relatedDimension.id}`}>View Dimension</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
          </div>
        </section>
      </div>
    );
  } catch {
    notFound();
  }
}
